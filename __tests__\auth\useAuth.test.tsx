import React from 'react';
import { renderHook, act } from '@testing-library/react-native';
import { AuthProvider } from '../../contexts/AuthContext';
import { useLogin, useRegister, useLogout, usePasswordReset } from '../../hooks/useAuth';
import { supabase, authService } from '../../utils/supabase';

// Mock Supabase
jest.mock('../../utils/supabase', () => ({
  supabase: {
    auth: {
      getSession: jest.fn(),
      onAuthStateChange: jest.fn(),
    },
  },
  authService: {
    signUp: jest.fn(),
    signIn: jest.fn(),
    signOut: jest.fn(),
    resetPassword: jest.fn(),
    updatePassword: jest.fn(),
    updateUserMetadata: jest.fn(),
  },
}));

const wrapper = ({ children }: { children: React.ReactNode }) => (
  <AuthProvider>{children}</AuthProvider>
);

describe('useLogin', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    (supabase.auth.getSession as jest.Mock).mockResolvedValue({
      data: { session: null },
    });
    (supabase.auth.onAuthStateChange as jest.Mock).mockReturnValue({
      data: { subscription: { unsubscribe: jest.fn() } },
    });
  });

  it('should handle successful login', async () => {
    (authService.signIn as jest.Mock).mockResolvedValue({
      user: { id: 'test-user', email: '<EMAIL>' },
    });

    const { result } = renderHook(() => useLogin(), { wrapper });

    expect(result.current.loading).toBe(false);
    expect(result.current.error).toBeNull();

    await act(async () => {
      await result.current.login('<EMAIL>', 'password123');
    });

    expect(authService.signIn).toHaveBeenCalledWith('<EMAIL>', 'password123');
    expect(result.current.loading).toBe(false);
    expect(result.current.error).toBeNull();
  });

  it('should handle login error', async () => {
    const errorMessage = 'Invalid credentials';
    (authService.signIn as jest.Mock).mockRejectedValue(new Error(errorMessage));

    const { result } = renderHook(() => useLogin(), { wrapper });

    await act(async () => {
      try {
        await result.current.login('<EMAIL>', 'wrongpassword');
      } catch (error) {
        // Expected to throw
      }
    });

    expect(result.current.error).toBe(errorMessage);
    expect(result.current.loading).toBe(false);
  });

  it('should clear error', async () => {
    const { result } = renderHook(() => useLogin(), { wrapper });

    // Set an error first
    act(() => {
      result.current.clearError();
    });

    expect(result.current.error).toBeNull();
  });
});

describe('useRegister', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    (supabase.auth.getSession as jest.Mock).mockResolvedValue({
      data: { session: null },
    });
    (supabase.auth.onAuthStateChange as jest.Mock).mockReturnValue({
      data: { subscription: { unsubscribe: jest.fn() } },
    });
  });

  it('should handle successful registration', async () => {
    (authService.signUp as jest.Mock).mockResolvedValue({
      user: { id: 'test-user', email: '<EMAIL>' },
    });

    const { result } = renderHook(() => useRegister(), { wrapper });

    await act(async () => {
      await result.current.register('<EMAIL>', 'password123', {
        nama: 'Test User',
      });
    });

    expect(authService.signUp).toHaveBeenCalledWith('<EMAIL>', 'password123', {
      nama: 'Test User',
    });
    expect(result.current.loading).toBe(false);
    expect(result.current.error).toBeNull();
  });

  it('should handle registration error', async () => {
    const errorMessage = 'Email already exists';
    (authService.signUp as jest.Mock).mockRejectedValue(new Error(errorMessage));

    const { result } = renderHook(() => useRegister(), { wrapper });

    await act(async () => {
      try {
        await result.current.register('<EMAIL>', 'password123');
      } catch (error) {
        // Expected to throw
      }
    });

    expect(result.current.error).toBe(errorMessage);
    expect(result.current.loading).toBe(false);
  });
});

describe('useLogout', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    (supabase.auth.getSession as jest.Mock).mockResolvedValue({
      data: { session: null },
    });
    (supabase.auth.onAuthStateChange as jest.Mock).mockReturnValue({
      data: { subscription: { unsubscribe: jest.fn() } },
    });
  });

  it('should handle successful logout', async () => {
    (authService.signOut as jest.Mock).mockResolvedValue(undefined);

    const { result } = renderHook(() => useLogout(), { wrapper });

    await act(async () => {
      await result.current.logout();
    });

    expect(authService.signOut).toHaveBeenCalled();
    expect(result.current.loading).toBe(false);
    expect(result.current.error).toBeNull();
  });

  it('should handle logout error', async () => {
    const errorMessage = 'Logout failed';
    (authService.signOut as jest.Mock).mockRejectedValue(new Error(errorMessage));

    const { result } = renderHook(() => useLogout(), { wrapper });

    await act(async () => {
      try {
        await result.current.logout();
      } catch (error) {
        // Expected to throw
      }
    });

    expect(result.current.error).toBe(errorMessage);
    expect(result.current.loading).toBe(false);
  });
});

describe('usePasswordReset', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    (supabase.auth.getSession as jest.Mock).mockResolvedValue({
      data: { session: null },
    });
    (supabase.auth.onAuthStateChange as jest.Mock).mockReturnValue({
      data: { subscription: { unsubscribe: jest.fn() } },
    });
  });

  it('should handle successful password reset', async () => {
    (authService.resetPassword as jest.Mock).mockResolvedValue(undefined);

    const { result } = renderHook(() => usePasswordReset(), { wrapper });

    await act(async () => {
      await result.current.sendResetEmail('<EMAIL>');
    });

    expect(authService.resetPassword).toHaveBeenCalledWith('<EMAIL>');
    expect(result.current.loading).toBe(false);
    expect(result.current.error).toBeNull();
    expect(result.current.success).toBe(true);
  });

  it('should handle password reset error', async () => {
    const errorMessage = 'Email not found';
    (authService.resetPassword as jest.Mock).mockRejectedValue(new Error(errorMessage));

    const { result } = renderHook(() => usePasswordReset(), { wrapper });

    await act(async () => {
      try {
        await result.current.sendResetEmail('<EMAIL>');
      } catch (error) {
        // Expected to throw
      }
    });

    expect(result.current.error).toBe(errorMessage);
    expect(result.current.loading).toBe(false);
    expect(result.current.success).toBe(false);
  });

  it('should clear error and success states', async () => {
    const { result } = renderHook(() => usePasswordReset(), { wrapper });

    act(() => {
      result.current.clearError();
      result.current.clearSuccess();
    });

    expect(result.current.error).toBeNull();
    expect(result.current.success).toBe(false);
  });
});
