# Troubleshooting Guide: java.io.IOException: Failed to download remote update

## Problem Description
The error "java.io.IOException: Failed to download remote update" occurs when Expo Go cannot download the JavaScript bundle from your development server.

## Solutions (Try in Order)

### 1. ✅ Clear Metro Cache (COMPLETED)
```bash
npx expo start --clear
```

### 2. ✅ Update Dependencies (COMPLETED)
```bash
npm install @react-native-async-storage/async-storage@2.1.2
npx expo install --fix
```

### 3. Network Connectivity Solutions

#### Option A: Use Tunnel Mode
```bash
npx expo start --tunnel
```
This creates a secure tunnel through Expo's servers, bypassing local network issues.

#### Option B: Check Network Configuration
1. Ensure your phone and computer are on the same WiFi network
2. Check if your router blocks device-to-device communication
3. Temporarily disable firewall/antivirus on your computer
4. Try using mobile hotspot from your phone

#### Option C: Use LAN Mode with Specific IP
```bash
npx expo start --lan
```

### 4. Expo Go App Solutions

#### Update Expo Go App
- Android: Update from Google Play Store
- iOS: Update from App Store

#### Clear Expo Go Cache
- Android: Settings > Apps > Expo Go > Storage > Clear Cache
- iOS: Delete and reinstall Expo Go

### 5. Development Build Alternative

If Expo Go continues to fail, switch to a development build:

```bash
# Install expo-dev-client
npx expo install expo-dev-client

# Create development build
npx expo run:android
# or
npx expo run:ios
```

### 6. USB Debugging (Android Only)

Enable USB debugging and connect via cable:
1. Enable Developer Options on Android
2. Enable USB Debugging
3. Connect phone via USB
4. Run: `npx expo start --localhost`

### 7. Port Configuration

If port 8081 is busy (as seen in your case), Expo automatically uses 8082. You can also manually specify:
```bash
npx expo start --port 8083
```

### 8. Reset Project (Last Resort)

```bash
# Clear all caches
npm start -- --reset-cache
rm -rf node_modules
npm install
npx expo start --clear
```

## Current Status
- ✅ Metro cache cleared and server restarted on port 8082
- ✅ Dependencies updated to compatible versions
- 🔄 Server running with fresh QR code

## Next Steps
1. Try scanning the new QR code with Expo Go
2. If still failing, try tunnel mode: `npx expo start --tunnel`
3. Consider switching to development build for better reliability

## Prevention Tips
- Keep Expo Go app updated
- Use stable WiFi networks
- Regularly clear Metro cache
- Keep dependencies in sync with Expo SDK version
