# Database Schema Documentation

## Overview
This document describes the database schema for the Tabungan Haji application using Supabase PostgreSQL.

## Tables

### 1. users
Stores user profile information.

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | UUID | PRIMARY KEY | Unique user identifier |
| nama | VARCHAR(255) | NOT NULL | User's full name |
| email | VARCHAR(255) | UNIQUE, NOT NULL | User's email address |
| telepon | VARCHAR(20) | | User's phone number |
| alamat | TEXT | | User's address |
| tanggal_lahir | DATE | | User's birth date |
| created_at | TIMESTAMP | DEFAULT NOW() | Record creation time |
| updated_at | TIMESTAMP | DEFAULT NOW() | Last update time |

### 2. targets
Stores user's Haji savings targets.

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | UUID | PRIMARY KEY | Unique target identifier |
| user_id | UUID | FOREIGN KEY | Reference to users table |
| target_biaya | BIGINT | NOT NULL, > 0 | Target amount in Rupiah |
| tanggal_target | DATE | NOT NULL | Target completion date |
| paket_haji | VARCHAR(20) | CHECK IN ('reguler', 'plus', 'khusus') | Haji package type |
| created_at | TIMESTAMP | DEFAULT NOW() | Record creation time |
| updated_at | TIMESTAMP | DEFAULT NOW() | Last update time |

### 3. tabungan_data
Stores aggregated savings data for each user.

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | UUID | PRIMARY KEY | Unique record identifier |
| user_id | UUID | FOREIGN KEY | Reference to users table |
| total_tabungan | BIGINT | DEFAULT 0, >= 0 | Total savings amount |
| last_updated | TIMESTAMP | DEFAULT NOW() | Last savings update |
| created_at | TIMESTAMP | DEFAULT NOW() | Record creation time |
| updated_at | TIMESTAMP | DEFAULT NOW() | Last update time |

### 4. setoran_history
Stores individual deposit/contribution records.

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | UUID | PRIMARY KEY | Unique deposit identifier |
| user_id | UUID | FOREIGN KEY | Reference to users table |
| tanggal | DATE | NOT NULL | Deposit date |
| jumlah | BIGINT | NOT NULL, > 0 | Deposit amount in Rupiah |
| keterangan | TEXT | | Deposit description/note |
| created_at | TIMESTAMP | DEFAULT NOW() | Record creation time |

### 5. app_settings
Stores user application preferences.

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | UUID | PRIMARY KEY | Unique setting identifier |
| user_id | UUID | FOREIGN KEY | Reference to users table |
| theme | VARCHAR(20) | CHECK IN ('light', 'dark', 'system') | UI theme preference |
| notifications | BOOLEAN | DEFAULT true | Notification preference |
| language | VARCHAR(5) | CHECK IN ('id', 'en') | Language preference |
| created_at | TIMESTAMP | DEFAULT NOW() | Record creation time |
| updated_at | TIMESTAMP | DEFAULT NOW() | Last update time |

## Relationships

- `targets.user_id` → `users.id` (One-to-One)
- `tabungan_data.user_id` → `users.id` (One-to-One)
- `setoran_history.user_id` → `users.id` (One-to-Many)
- `app_settings.user_id` → `users.id` (One-to-One)

## Indexes

- `idx_targets_user_id` on `targets(user_id)`
- `idx_tabungan_data_user_id` on `tabungan_data(user_id)`
- `idx_setoran_history_user_id` on `setoran_history(user_id)`
- `idx_setoran_history_tanggal` on `setoran_history(tanggal DESC)`
- `idx_app_settings_user_id` on `app_settings(user_id)`

## Triggers

### Auto-update timestamps
- Updates `updated_at` column automatically on record updates for:
  - users
  - targets
  - tabungan_data
  - app_settings

### Auto-calculate total savings
- `update_total_tabungan()` function automatically recalculates `total_tabungan` in `tabungan_data` when:
  - New deposit is added to `setoran_history`
  - Existing deposit is updated
  - Deposit is deleted

## Security

- Row Level Security (RLS) is enabled on all tables
- Currently configured with permissive policies for development
- In production, implement proper user authentication and authorization policies

## Setup Instructions

1. Run the SQL commands in `schema.sql` in your Supabase SQL editor
2. Verify all tables are created successfully
3. Test the triggers by inserting sample data
4. Configure proper RLS policies for production use
