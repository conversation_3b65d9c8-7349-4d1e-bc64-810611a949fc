import React from 'react';
import { render, waitFor, act } from '@testing-library/react-native';
import { AuthProvider, useAuth } from '../../contexts/AuthContext';
import { supabase } from '../../utils/supabase';

// Mock Supabase
jest.mock('../../utils/supabase', () => ({
  supabase: {
    auth: {
      getSession: jest.fn(),
      onAuthStateChange: jest.fn(),
      signUp: jest.fn(),
      signInWithPassword: jest.fn(),
      signOut: jest.fn(),
      resetPasswordForEmail: jest.fn(),
      updateUser: jest.fn(),
      getUser: jest.fn(),
    },
  },
  authService: {
    signUp: jest.fn(),
    signIn: jest.fn(),
    signOut: jest.fn(),
    resetPassword: jest.fn(),
    updatePassword: jest.fn(),
    updateUserMetadata: jest.fn(),
  },
}));

// Test component to access auth context
const TestComponent = () => {
  const auth = useAuth();
  return null;
};

describe('AuthContext', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should provide auth context', () => {
    const mockSession = {
      access_token: 'test-token',
      user: {
        id: 'test-user-id',
        email: '<EMAIL>',
      },
    };

    (supabase.auth.getSession as jest.Mock).mockResolvedValue({
      data: { session: mockSession },
    });

    (supabase.auth.onAuthStateChange as jest.Mock).mockReturnValue({
      data: { subscription: { unsubscribe: jest.fn() } },
    });

    render(
      <AuthProvider>
        <TestComponent />
      </AuthProvider>
    );

    expect(supabase.auth.getSession).toHaveBeenCalled();
    expect(supabase.auth.onAuthStateChange).toHaveBeenCalled();
  });

  it('should handle initial session loading', async () => {
    const mockSession = {
      access_token: 'test-token',
      user: {
        id: 'test-user-id',
        email: '<EMAIL>',
      },
    };

    (supabase.auth.getSession as jest.Mock).mockResolvedValue({
      data: { session: mockSession },
    });

    (supabase.auth.onAuthStateChange as jest.Mock).mockReturnValue({
      data: { subscription: { unsubscribe: jest.fn() } },
    });

    let authContext: any;
    const TestComponentWithContext = () => {
      authContext = useAuth();
      return null;
    };

    render(
      <AuthProvider>
        <TestComponentWithContext />
      </AuthProvider>
    );

    await waitFor(() => {
      expect(authContext.loading).toBe(false);
    });

    expect(authContext.user).toEqual(mockSession.user);
    expect(authContext.session).toEqual(mockSession);
  });

  it('should handle auth state changes', async () => {
    let authStateCallback: any;
    
    (supabase.auth.getSession as jest.Mock).mockResolvedValue({
      data: { session: null },
    });

    (supabase.auth.onAuthStateChange as jest.Mock).mockImplementation((callback) => {
      authStateCallback = callback;
      return {
        data: { subscription: { unsubscribe: jest.fn() } },
      };
    });

    let authContext: any;
    const TestComponentWithContext = () => {
      authContext = useAuth();
      return null;
    };

    render(
      <AuthProvider>
        <TestComponentWithContext />
      </AuthProvider>
    );

    // Wait for initial load
    await waitFor(() => {
      expect(authContext.loading).toBe(false);
    });

    expect(authContext.user).toBeNull();

    // Simulate sign in
    const mockSession = {
      access_token: 'test-token',
      user: {
        id: 'test-user-id',
        email: '<EMAIL>',
      },
    };

    act(() => {
      authStateCallback('SIGNED_IN', mockSession);
    });

    await waitFor(() => {
      expect(authContext.user).toEqual(mockSession.user);
      expect(authContext.session).toEqual(mockSession);
    });
  });

  it('should handle sign out', async () => {
    let authStateCallback: any;
    
    const mockSession = {
      access_token: 'test-token',
      user: {
        id: 'test-user-id',
        email: '<EMAIL>',
      },
    };

    (supabase.auth.getSession as jest.Mock).mockResolvedValue({
      data: { session: mockSession },
    });

    (supabase.auth.onAuthStateChange as jest.Mock).mockImplementation((callback) => {
      authStateCallback = callback;
      return {
        data: { subscription: { unsubscribe: jest.fn() } },
      };
    });

    let authContext: any;
    const TestComponentWithContext = () => {
      authContext = useAuth();
      return null;
    };

    render(
      <AuthProvider>
        <TestComponentWithContext />
      </AuthProvider>
    );

    // Wait for initial load
    await waitFor(() => {
      expect(authContext.user).toEqual(mockSession.user);
    });

    // Simulate sign out
    act(() => {
      authStateCallback('SIGNED_OUT', null);
    });

    await waitFor(() => {
      expect(authContext.user).toBeNull();
      expect(authContext.session).toBeNull();
    });
  });

  it('should throw error when used outside provider', () => {
    // Suppress console.error for this test
    const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {});

    expect(() => {
      render(<TestComponent />);
    }).toThrow('useAuth must be used within an AuthProvider');

    consoleSpy.mockRestore();
  });
});
