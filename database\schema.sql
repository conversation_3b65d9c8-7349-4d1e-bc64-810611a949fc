-- Tabungan Haji Database Schema for Supabase
-- This file contains the SQL commands to create all necessary tables

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Users table
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    nama VARCHAR(255) NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    telepon VARCHAR(20),
    alamat TEXT,
    tanggal_lahir DATE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Targets table
CREATE TABLE targets (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    target_biaya BIGINT NOT NULL CHECK (target_biaya > 0),
    tanggal_target DATE NOT NULL,
    paket_haji VARCHAR(20) NOT NULL CHECK (paket_haji IN ('reguler', 'plus', 'khusus')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Tabungan data table
CREATE TABLE tabungan_data (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    total_tabungan BIGINT NOT NULL DEFAULT 0 CHECK (total_tabungan >= 0),
    last_updated TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Setoran history table
CREATE TABLE setoran_history (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    tanggal DATE NOT NULL,
    jumlah BIGINT NOT NULL CHECK (jumlah > 0),
    keterangan TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- App settings table
CREATE TABLE app_settings (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    theme VARCHAR(20) NOT NULL DEFAULT 'system' CHECK (theme IN ('light', 'dark', 'system')),
    notifications BOOLEAN NOT NULL DEFAULT true,
    language VARCHAR(5) NOT NULL DEFAULT 'id' CHECK (language IN ('id', 'en')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes for better performance
CREATE INDEX idx_targets_user_id ON targets(user_id);
CREATE INDEX idx_tabungan_data_user_id ON tabungan_data(user_id);
CREATE INDEX idx_setoran_history_user_id ON setoran_history(user_id);
CREATE INDEX idx_setoran_history_tanggal ON setoran_history(tanggal DESC);
CREATE INDEX idx_app_settings_user_id ON app_settings(user_id);

-- Unique constraints
ALTER TABLE targets ADD CONSTRAINT unique_user_target UNIQUE (user_id);
ALTER TABLE tabungan_data ADD CONSTRAINT unique_user_tabungan UNIQUE (user_id);
ALTER TABLE app_settings ADD CONSTRAINT unique_user_settings UNIQUE (user_id);

-- Triggers to update updated_at timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_targets_updated_at BEFORE UPDATE ON targets
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_tabungan_data_updated_at BEFORE UPDATE ON tabungan_data
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_app_settings_updated_at BEFORE UPDATE ON app_settings
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Function to automatically update total_tabungan when setoran is added/updated/deleted
CREATE OR REPLACE FUNCTION update_total_tabungan()
RETURNS TRIGGER AS $$
BEGIN
    -- Update total_tabungan for the affected user
    UPDATE tabungan_data 
    SET total_tabungan = (
        SELECT COALESCE(SUM(jumlah), 0) 
        FROM setoran_history 
        WHERE user_id = COALESCE(NEW.user_id, OLD.user_id)
    ),
    last_updated = NOW()
    WHERE user_id = COALESCE(NEW.user_id, OLD.user_id);
    
    RETURN COALESCE(NEW, OLD);
END;
$$ language 'plpgsql';

CREATE TRIGGER trigger_update_total_tabungan_insert
    AFTER INSERT ON setoran_history
    FOR EACH ROW EXECUTE FUNCTION update_total_tabungan();

CREATE TRIGGER trigger_update_total_tabungan_update
    AFTER UPDATE ON setoran_history
    FOR EACH ROW EXECUTE FUNCTION update_total_tabungan();

CREATE TRIGGER trigger_update_total_tabungan_delete
    AFTER DELETE ON setoran_history
    FOR EACH ROW EXECUTE FUNCTION update_total_tabungan();

-- Row Level Security (RLS) policies
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE targets ENABLE ROW LEVEL SECURITY;
ALTER TABLE tabungan_data ENABLE ROW LEVEL SECURITY;
ALTER TABLE setoran_history ENABLE ROW LEVEL SECURITY;
ALTER TABLE app_settings ENABLE ROW LEVEL SECURITY;

-- For now, allow all operations (in production, implement proper user authentication)
CREATE POLICY "Allow all operations on users" ON users FOR ALL USING (true);
CREATE POLICY "Allow all operations on targets" ON targets FOR ALL USING (true);
CREATE POLICY "Allow all operations on tabungan_data" ON tabungan_data FOR ALL USING (true);
CREATE POLICY "Allow all operations on setoran_history" ON setoran_history FOR ALL USING (true);
CREATE POLICY "Allow all operations on app_settings" ON app_settings FOR ALL USING (true);
