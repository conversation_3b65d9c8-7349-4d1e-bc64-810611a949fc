import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { supabase, authService, AuthUser, AuthSession } from '../utils/supabase';

// Authentication context types
interface AuthContextType {
  user: AuthUser | null;
  session: AuthSession | null;
  loading: boolean;
  signUp: (email: string, password: string, userData?: {
    nama: string;
    telepon?: string;
    alamat?: string;
    tanggal_lahir?: string;
  }) => Promise<void>;
  signIn: (email: string, password: string) => Promise<void>;
  signOut: () => Promise<void>;
  resetPassword: (email: string) => Promise<void>;
  updatePassword: (newPassword: string) => Promise<void>;
  updateUserMetadata: (metadata: Record<string, any>) => Promise<void>;
}

// Create the context
const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Auth provider component
interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<AuthUser | null>(null);
  const [session, setSession] = useState<AuthSession | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Get initial session
    const getInitialSession = async () => {
      try {
        const { data: { session } } = await supabase.auth.getSession();
        setSession(session);
        setUser(session?.user || null);
      } catch (error) {
        console.error('Error getting initial session:', error);
      } finally {
        setLoading(false);
      }
    };

    getInitialSession();

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        console.log('Auth state changed:', event, session?.user?.email);
        setSession(session);
        setUser(session?.user || null);
        setLoading(false);
      }
    );

    return () => {
      subscription.unsubscribe();
    };
  }, []);

  // Sign up function
  const signUp = async (
    email: string, 
    password: string, 
    userData?: {
      nama: string;
      telepon?: string;
      alamat?: string;
      tanggal_lahir?: string;
    }
  ) => {
    setLoading(true);
    try {
      await authService.signUp(email, password, userData);
    } finally {
      setLoading(false);
    }
  };

  // Sign in function
  const signIn = async (email: string, password: string) => {
    setLoading(true);
    try {
      await authService.signIn(email, password);
    } finally {
      setLoading(false);
    }
  };

  // Sign out function
  const signOut = async () => {
    setLoading(true);
    try {
      await authService.signOut();
    } finally {
      setLoading(false);
    }
  };

  // Reset password function
  const resetPassword = async (email: string) => {
    await authService.resetPassword(email);
  };

  // Update password function
  const updatePassword = async (newPassword: string) => {
    await authService.updatePassword(newPassword);
  };

  // Update user metadata function
  const updateUserMetadata = async (metadata: Record<string, any>) => {
    await authService.updateUserMetadata(metadata);
  };

  const value: AuthContextType = {
    user,
    session,
    loading,
    signUp,
    signIn,
    signOut,
    resetPassword,
    updatePassword,
    updateUserMetadata,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

// Custom hook to use auth context
export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

// Hook to check if user is authenticated
export const useAuthUser = () => {
  const { user, loading } = useAuth();
  return { user, loading, isAuthenticated: !!user };
};

// Hook for protected routes
export const useRequireAuth = () => {
  const { user, loading } = useAuth();
  
  if (loading) {
    return { user: null, loading: true, isAuthenticated: false };
  }
  
  return { user, loading: false, isAuthenticated: !!user };
};
