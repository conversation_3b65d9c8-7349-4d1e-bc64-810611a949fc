# 🛠️ Development Guide - Tabungan Haji

Panduan lengkap untuk pengembangan aplikasi Tabungan Haji.

## 🏗️ Architecture Overview

### App Structure
```
Tabungan Haji App
├── Presentation Layer (React Native Components)
├── Business Logic Layer (Custom Hooks)
├── Data Layer (AsyncStorage + Utilities)
└── UI Layer (Reusable Components)
```

### Design Patterns
- **Component Composition**: Reusable UI components
- **Custom Hooks**: Business logic separation
- **Provider Pattern**: State management
- **Repository Pattern**: Data access abstraction

## 📁 File Organization

### Naming Conventions
- **Components**: PascalCase (e.g., `Button.tsx`, `UserProfile.tsx`)
- **Hooks**: camelCase with 'use' prefix (e.g., `useTabunganData.ts`)
- **Utils**: camelCase (e.g., `formatters.ts`, `storage.ts`)
- **Constants**: UPPER_SNAKE_CASE (e.g., `STORAGE_KEYS`)

### Import Order
1. React imports
2. Third-party libraries
3. Internal components
4. Utils and constants
5. Types and interfaces

```typescript
import React, { useState, useEffect } from 'react';
import { View, Text } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

import { Button } from '@/components/ui/Button';
import { Card } from '@/components/ui/Card';

import { formatRupiah } from '@/utils/formatters';
import { Colors } from '@/constants/Colors';

import type { UserData } from '@/utils/storage';
```

## 🎨 UI/UX Guidelines

### Color Usage
```typescript
// Primary colors for main actions
colors.tint // Main brand color (green)

// Background colors
colors.background // Adaptive background

// Text colors
colors.text // Adaptive text color

// Status colors
'#4CAF50' // Success (green)
'#F44336' // Error (red)
'#FF9800' // Warning (orange)
'#2196F3' // Info (blue)
```

### Typography Scale
```typescript
const typography = {
  title: { fontSize: 24, fontWeight: 'bold' },
  subtitle: { fontSize: 18, fontWeight: '600' },
  body: { fontSize: 16, fontWeight: 'normal' },
  caption: { fontSize: 14, fontWeight: 'normal' },
  small: { fontSize: 12, fontWeight: 'normal' },
};
```

### Spacing System
```typescript
const spacing = {
  xs: 4,   // Very tight spacing
  sm: 8,   // Tight spacing
  md: 16,  // Normal spacing
  lg: 24,  // Loose spacing
  xl: 32,  // Very loose spacing
};
```

## 🔧 Component Development

### Creating New Components

1. **Create component file**
```typescript
// components/ui/NewComponent.tsx
import React from 'react';
import { View, Text, StyleSheet } from 'react-native';

interface NewComponentProps {
  title: string;
  onPress?: () => void;
}

export function NewComponent({ title, onPress }: NewComponentProps) {
  return (
    <View style={styles.container}>
      <Text style={styles.title}>{title}</Text>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    padding: 16,
  },
  title: {
    fontSize: 16,
    fontWeight: '600',
  },
});
```

2. **Export from index file**
```typescript
// components/ui/index.ts
export { Button } from './Button';
export { Card } from './Card';
export { NewComponent } from './NewComponent';
```

### Component Best Practices
- Always use TypeScript interfaces for props
- Provide default values for optional props
- Use StyleSheet.create for styles
- Make components reusable and configurable
- Add proper error handling

## 🪝 Custom Hooks

### Hook Structure
```typescript
// hooks/useCustomHook.ts
import { useState, useEffect } from 'react';

export function useCustomHook(initialValue: string) {
  const [value, setValue] = useState(initialValue);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const updateValue = async (newValue: string) => {
    try {
      setLoading(true);
      setError(null);
      // Async operation
      setValue(newValue);
    } catch (err) {
      setError('Something went wrong');
    } finally {
      setLoading(false);
    }
  };

  return {
    value,
    loading,
    error,
    updateValue,
  };
}
```

### Hook Best Practices
- Always handle loading and error states
- Use meaningful return object keys
- Provide cleanup functions when needed
- Keep hooks focused on single responsibility

## 💾 Data Management

### Storage Patterns
```typescript
// utils/storage.ts
export const dataStorage = {
  async save<T>(key: string, data: T): Promise<void> {
    try {
      const jsonData = JSON.stringify(data);
      await AsyncStorage.setItem(key, jsonData);
    } catch (error) {
      console.error('Storage save error:', error);
      throw error;
    }
  },

  async load<T>(key: string): Promise<T | null> {
    try {
      const jsonData = await AsyncStorage.getItem(key);
      return jsonData ? JSON.parse(jsonData) : null;
    } catch (error) {
      console.error('Storage load error:', error);
      return null;
    }
  },
};
```

### Data Validation
```typescript
// utils/validation.ts
export const validators = {
  isValidEmail: (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  },

  isValidPhone: (phone: string): boolean => {
    const phoneRegex = /^(\+62|62|0)8[1-9][0-9]{6,9}$/;
    return phoneRegex.test(phone);
  },

  isValidAmount: (amount: number): boolean => {
    return amount > 0 && amount <= 1000000000; // Max 1 billion
  },
};
```

## 🧪 Testing Strategy

### Unit Tests
```typescript
// __tests__/formatters.test.ts
import { formatRupiah } from '@/utils/formatters';

describe('formatRupiah', () => {
  it('should format numbers correctly', () => {
    expect(formatRupiah(1000000)).toBe('Rp1.000.000');
    expect(formatRupiah(0)).toBe('Rp0');
  });

  it('should handle negative numbers', () => {
    expect(formatRupiah(-1000)).toBe('-Rp1.000');
  });
});
```

### Integration Tests
```typescript
// __tests__/hooks.test.ts
import { renderHook, act } from '@testing-library/react-hooks';
import { useTabunganData } from '@/hooks/useTabunganData';

describe('useTabunganData', () => {
  it('should load initial data', async () => {
    const { result, waitForNextUpdate } = renderHook(() => useTabunganData());
    
    await waitForNextUpdate();
    
    expect(result.current.loading).toBe(false);
    expect(result.current.tabunganData).toBeDefined();
  });
});
```

## 🚀 Performance Optimization

### React Native Performance
1. **Use FlatList for large lists**
```typescript
<FlatList
  data={riwayatSetoran}
  keyExtractor={(item) => item.id.toString()}
  renderItem={({ item }) => <SetoranItem item={item} />}
  removeClippedSubviews={true}
  maxToRenderPerBatch={10}
  windowSize={10}
/>
```

2. **Optimize images**
```typescript
<Image
  source={{ uri: imageUrl }}
  style={styles.image}
  resizeMode="cover"
  cache="force-cache"
/>
```

3. **Use React.memo for expensive components**
```typescript
export const ExpensiveComponent = React.memo(({ data }) => {
  // Expensive rendering logic
  return <View>{/* Complex UI */}</View>;
});
```

### Bundle Size Optimization
- Use dynamic imports for large libraries
- Remove unused dependencies
- Optimize images and assets
- Use Hermes JavaScript engine

## 🔒 Security Best Practices

### Data Protection
```typescript
// utils/security.ts
import CryptoJS from 'crypto-js';

const SECRET_KEY = 'your-secret-key';

export const security = {
  encrypt: (data: string): string => {
    return CryptoJS.AES.encrypt(data, SECRET_KEY).toString();
  },

  decrypt: (encryptedData: string): string => {
    const bytes = CryptoJS.AES.decrypt(encryptedData, SECRET_KEY);
    return bytes.toString(CryptoJS.enc.Utf8);
  },
};
```

### Input Validation
- Always validate user inputs
- Sanitize data before storage
- Use TypeScript for type safety
- Implement proper error handling

## 📱 Platform-Specific Considerations

### iOS Specific
- Use SafeAreaView for notch handling
- Consider iOS design guidelines
- Test on different screen sizes

### Android Specific
- Handle back button properly
- Consider Android design patterns
- Test on various Android versions

### Web Specific
- Responsive design for different screen sizes
- Keyboard navigation support
- SEO considerations

## 🔄 State Management

### Local State (useState)
```typescript
const [count, setCount] = useState(0);
const [user, setUser] = useState<User | null>(null);
```

### Complex State (useReducer)
```typescript
const [state, dispatch] = useReducer(reducer, initialState);
```

### Global State (Context)
```typescript
const TabunganContext = createContext<TabunganContextType | null>(null);

export function TabunganProvider({ children }: { children: ReactNode }) {
  const [tabunganData, setTabunganData] = useState<TabunganData | null>(null);
  
  return (
    <TabunganContext.Provider value={{ tabunganData, setTabunganData }}>
      {children}
    </TabunganContext.Provider>
  );
}
```

## 🐛 Debugging

### Common Issues
1. **AsyncStorage not working**: Check permissions
2. **Navigation issues**: Verify route names
3. **Styling problems**: Check platform differences
4. **Performance issues**: Use React DevTools

### Debugging Tools
- React Native Debugger
- Flipper
- Console.log strategically
- Error boundaries for crash handling

## 📦 Build & Deployment

### Development Build
```bash
npx expo start --dev-client
```

### Production Build
```bash
eas build --platform all --profile production
```

### Testing Build
```bash
eas build --platform all --profile preview
```

## 🔧 Troubleshooting

### Common Problems
1. **Metro bundler issues**: Clear cache with `npx expo start --clear`
2. **Dependency conflicts**: Check package versions
3. **Build failures**: Check native dependencies
4. **Performance issues**: Profile with React DevTools

### Solutions
- Keep dependencies updated
- Use exact versions in package.json
- Test on real devices regularly
- Monitor app performance metrics
