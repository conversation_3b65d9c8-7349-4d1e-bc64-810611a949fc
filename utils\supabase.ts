import { createClient } from '@supabase/supabase-js';

// Supabase configuration
const supabaseUrl = 'https://xqyvbbspkvocjehgrfkn.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InhxeXZiYnNwa3ZvY2plaGdyZmtuIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTA4MTg5NjYsImV4cCI6MjA2NjM5NDk2Nn0.l80brocsjncrXfQTn_zAr4ACfjQWZQyMnnpy1-TP7Uc';

// Create Supabase client
export const supabase = createClient(supabaseUrl, supabaseKey);

// Database table names
export const TABLES = {
  USERS: 'users',
  TARGETS: 'targets', 
  TABUNGAN_DATA: 'tabungan_data',
  SETORAN_HISTORY: 'setoran_history',
  APP_SETTINGS: 'app_settings',
} as const;

// Type definitions for database tables
export interface DatabaseUser {
  id: string;
  nama: string;
  email: string;
  telepon: string;
  alamat: string;
  tanggal_lahir: string;
  created_at: string;
  updated_at: string;
}

export interface DatabaseTarget {
  id: string;
  user_id: string;
  target_biaya: number;
  tanggal_target: string;
  paket_haji: 'reguler' | 'plus' | 'khusus';
  created_at: string;
  updated_at: string;
}

export interface DatabaseTabunganData {
  id: string;
  user_id: string;
  total_tabungan: number;
  last_updated: string;
  created_at: string;
  updated_at: string;
}

export interface DatabaseSetoranHistory {
  id: string;
  user_id: string;
  tanggal: string;
  jumlah: number;
  keterangan: string;
  created_at: string;
}

export interface DatabaseAppSettings {
  id: string;
  user_id: string;
  theme: 'light' | 'dark' | 'system';
  notifications: boolean;
  language: 'id' | 'en';
  created_at: string;
  updated_at: string;
}

// Helper function to handle Supabase errors
export const handleSupabaseError = (error: any, operation: string) => {
  console.error(`Supabase ${operation} error:`, error);
  
  if (error?.message) {
    return error.message;
  }
  
  return `Gagal melakukan operasi ${operation}`;
};

// Helper function to generate user ID (for demo purposes)
// In a real app, this would come from authentication
export const getCurrentUserId = (): string => {
  // For now, return a fixed user ID
  // This should be replaced with actual user authentication
  return 'demo-user-id';
};

// User Service Functions
export const userService = {
  async createUser(userData: Omit<DatabaseUser, 'id' | 'created_at' | 'updated_at'>): Promise<DatabaseUser> {
    const { data, error } = await supabase
      .from(TABLES.USERS)
      .insert([{
        nama: userData.nama,
        email: userData.email,
        telepon: userData.telepon,
        alamat: userData.alamat,
        tanggal_lahir: userData.tanggal_lahir,
      }])
      .select()
      .single();

    if (error) {
      throw new Error(handleSupabaseError(error, 'create user'));
    }

    return data;
  },

  async getUserById(userId: string): Promise<DatabaseUser | null> {
    const { data, error } = await supabase
      .from(TABLES.USERS)
      .select('*')
      .eq('id', userId)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        return null; // No rows returned
      }
      throw new Error(handleSupabaseError(error, 'get user'));
    }

    return data;
  },

  async getUserByEmail(email: string): Promise<DatabaseUser | null> {
    const { data, error } = await supabase
      .from(TABLES.USERS)
      .select('*')
      .eq('email', email)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        return null; // No rows returned
      }
      throw new Error(handleSupabaseError(error, 'get user by email'));
    }

    return data;
  },

  async updateUser(userId: string, updates: Partial<Omit<DatabaseUser, 'id' | 'created_at' | 'updated_at'>>): Promise<DatabaseUser> {
    const { data, error } = await supabase
      .from(TABLES.USERS)
      .update(updates)
      .eq('id', userId)
      .select()
      .single();

    if (error) {
      throw new Error(handleSupabaseError(error, 'update user'));
    }

    return data;
  },

  async deleteUser(userId: string): Promise<void> {
    const { error } = await supabase
      .from(TABLES.USERS)
      .delete()
      .eq('id', userId);

    if (error) {
      throw new Error(handleSupabaseError(error, 'delete user'));
    }
  },
};

// Target Service Functions
export const targetService = {
  async createTarget(targetData: Omit<DatabaseTarget, 'id' | 'created_at' | 'updated_at'>): Promise<DatabaseTarget> {
    const { data, error } = await supabase
      .from(TABLES.TARGETS)
      .insert([targetData])
      .select()
      .single();

    if (error) {
      throw new Error(handleSupabaseError(error, 'create target'));
    }

    return data;
  },

  async getTargetByUserId(userId: string): Promise<DatabaseTarget | null> {
    const { data, error } = await supabase
      .from(TABLES.TARGETS)
      .select('*')
      .eq('user_id', userId)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        return null; // No rows returned
      }
      throw new Error(handleSupabaseError(error, 'get target'));
    }

    return data;
  },

  async updateTarget(userId: string, updates: Partial<Omit<DatabaseTarget, 'id' | 'user_id' | 'created_at' | 'updated_at'>>): Promise<DatabaseTarget> {
    const { data, error } = await supabase
      .from(TABLES.TARGETS)
      .update(updates)
      .eq('user_id', userId)
      .select()
      .single();

    if (error) {
      throw new Error(handleSupabaseError(error, 'update target'));
    }

    return data;
  },

  async deleteTarget(userId: string): Promise<void> {
    const { error } = await supabase
      .from(TABLES.TARGETS)
      .delete()
      .eq('user_id', userId);

    if (error) {
      throw new Error(handleSupabaseError(error, 'delete target'));
    }
  },
};

// Tabungan Data Service Functions
export const tabunganService = {
  async createTabunganData(userId: string): Promise<DatabaseTabunganData> {
    const { data, error } = await supabase
      .from(TABLES.TABUNGAN_DATA)
      .insert([{
        user_id: userId,
        total_tabungan: 0,
        last_updated: new Date().toISOString(),
      }])
      .select()
      .single();

    if (error) {
      throw new Error(handleSupabaseError(error, 'create tabungan data'));
    }

    return data;
  },

  async getTabunganDataByUserId(userId: string): Promise<DatabaseTabunganData | null> {
    const { data, error } = await supabase
      .from(TABLES.TABUNGAN_DATA)
      .select('*')
      .eq('user_id', userId)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        return null; // No rows returned
      }
      throw new Error(handleSupabaseError(error, 'get tabungan data'));
    }

    return data;
  },

  async updateTabunganData(userId: string, updates: Partial<Omit<DatabaseTabunganData, 'id' | 'user_id' | 'created_at' | 'updated_at'>>): Promise<DatabaseTabunganData> {
    const { data, error } = await supabase
      .from(TABLES.TABUNGAN_DATA)
      .update({
        ...updates,
        last_updated: new Date().toISOString(),
      })
      .eq('user_id', userId)
      .select()
      .single();

    if (error) {
      throw new Error(handleSupabaseError(error, 'update tabungan data'));
    }

    return data;
  },
};

// Setoran History Service Functions
export const setoranService = {
  async addSetoran(setoranData: Omit<DatabaseSetoranHistory, 'id' | 'created_at'>): Promise<DatabaseSetoranHistory> {
    const { data, error } = await supabase
      .from(TABLES.SETORAN_HISTORY)
      .insert([setoranData])
      .select()
      .single();

    if (error) {
      throw new Error(handleSupabaseError(error, 'add setoran'));
    }

    return data;
  },

  async getSetoranHistoryByUserId(userId: string, limit?: number): Promise<DatabaseSetoranHistory[]> {
    let query = supabase
      .from(TABLES.SETORAN_HISTORY)
      .select('*')
      .eq('user_id', userId)
      .order('tanggal', { ascending: false });

    if (limit) {
      query = query.limit(limit);
    }

    const { data, error } = await query;

    if (error) {
      throw new Error(handleSupabaseError(error, 'get setoran history'));
    }

    return data || [];
  },

  async updateSetoran(setoranId: string, updates: Partial<Omit<DatabaseSetoranHistory, 'id' | 'user_id' | 'created_at'>>): Promise<DatabaseSetoranHistory> {
    const { data, error } = await supabase
      .from(TABLES.SETORAN_HISTORY)
      .update(updates)
      .eq('id', setoranId)
      .select()
      .single();

    if (error) {
      throw new Error(handleSupabaseError(error, 'update setoran'));
    }

    return data;
  },

  async deleteSetoran(setoranId: string): Promise<void> {
    const { error } = await supabase
      .from(TABLES.SETORAN_HISTORY)
      .delete()
      .eq('id', setoranId);

    if (error) {
      throw new Error(handleSupabaseError(error, 'delete setoran'));
    }
  },

  async getSetoranStatistics(userId: string): Promise<{
    totalSetoran: number;
    averageSetoran: number;
    largestSetoran: number;
    smallestSetoran: number;
    monthlyAverage: number;
  }> {
    const { data, error } = await supabase
      .from(TABLES.SETORAN_HISTORY)
      .select('jumlah, tanggal')
      .eq('user_id', userId);

    if (error) {
      throw new Error(handleSupabaseError(error, 'get setoran statistics'));
    }

    if (!data || data.length === 0) {
      return {
        totalSetoran: 0,
        averageSetoran: 0,
        largestSetoran: 0,
        smallestSetoran: 0,
        monthlyAverage: 0,
      };
    }

    const amounts = data.map(item => item.jumlah);
    const totalSetoran = data.length;
    const totalAmount = amounts.reduce((sum, amount) => sum + amount, 0);
    const averageSetoran = totalAmount / totalSetoran;
    const largestSetoran = Math.max(...amounts);
    const smallestSetoran = Math.min(...amounts);

    // Calculate monthly average (last 6 months)
    const sixMonthsAgo = new Date();
    sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 6);

    const recentSetoran = data.filter(
      item => new Date(item.tanggal) >= sixMonthsAgo
    );

    const monthlyAverage = recentSetoran.length > 0
      ? recentSetoran.reduce((sum, item) => sum + item.jumlah, 0) / 6
      : 0;

    return {
      totalSetoran,
      averageSetoran,
      largestSetoran,
      smallestSetoran,
      monthlyAverage,
    };
  },
};

// App Settings Service Functions
export const settingsService = {
  async createSettings(userId: string, settings?: Partial<Omit<DatabaseAppSettings, 'id' | 'user_id' | 'created_at' | 'updated_at'>>): Promise<DatabaseAppSettings> {
    const { data, error } = await supabase
      .from(TABLES.APP_SETTINGS)
      .insert([{
        user_id: userId,
        theme: settings?.theme || 'system',
        notifications: settings?.notifications ?? true,
        language: settings?.language || 'id',
      }])
      .select()
      .single();

    if (error) {
      throw new Error(handleSupabaseError(error, 'create settings'));
    }

    return data;
  },

  async getSettingsByUserId(userId: string): Promise<DatabaseAppSettings | null> {
    const { data, error } = await supabase
      .from(TABLES.APP_SETTINGS)
      .select('*')
      .eq('user_id', userId)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        return null; // No rows returned
      }
      throw new Error(handleSupabaseError(error, 'get settings'));
    }

    return data;
  },

  async updateSettings(userId: string, updates: Partial<Omit<DatabaseAppSettings, 'id' | 'user_id' | 'created_at' | 'updated_at'>>): Promise<DatabaseAppSettings> {
    const { data, error } = await supabase
      .from(TABLES.APP_SETTINGS)
      .update(updates)
      .eq('user_id', userId)
      .select()
      .single();

    if (error) {
      throw new Error(handleSupabaseError(error, 'update settings'));
    }

    return data;
  },

  async deleteSettings(userId: string): Promise<void> {
    const { error } = await supabase
      .from(TABLES.APP_SETTINGS)
      .delete()
      .eq('user_id', userId);

    if (error) {
      throw new Error(handleSupabaseError(error, 'delete settings'));
    }
  },
};

// Combined service functions for easier migration from storage.ts
export const combinedService = {
  // Initialize user with all related data
  async initializeUser(userData: {
    nama: string;
    email: string;
    telepon: string;
    alamat: string;
    tanggal_lahir: string;
  }, targetData?: {
    target_biaya: number;
    tanggal_target: string;
    paket_haji: 'reguler' | 'plus' | 'khusus';
  }): Promise<{
    user: DatabaseUser;
    target?: DatabaseTarget;
    tabunganData: DatabaseTabunganData;
    settings: DatabaseAppSettings;
  }> {
    // Create user
    const user = await userService.createUser({
      nama: userData.nama,
      email: userData.email,
      telepon: userData.telepon,
      alamat: userData.alamat,
      tanggal_lahir: userData.tanggal_lahir,
    });

    // Create tabungan data
    const tabunganData = await tabunganService.createTabunganData(user.id);

    // Create settings
    const settings = await settingsService.createSettings(user.id);

    // Create target if provided
    let target: DatabaseTarget | undefined;
    if (targetData) {
      target = await targetService.createTarget({
        user_id: user.id,
        target_biaya: targetData.target_biaya,
        tanggal_target: targetData.tanggal_target,
        paket_haji: targetData.paket_haji,
      });
    }

    return {
      user,
      target,
      tabunganData,
      settings,
    };
  },

  // Get all user data
  async getAllUserData(userId: string): Promise<{
    user: DatabaseUser | null;
    target: DatabaseTarget | null;
    tabunganData: DatabaseTabunganData | null;
    setoranHistory: DatabaseSetoranHistory[];
    settings: DatabaseAppSettings | null;
  }> {
    const [user, target, tabunganData, setoranHistory, settings] = await Promise.all([
      userService.getUserById(userId),
      targetService.getTargetByUserId(userId),
      tabunganService.getTabunganDataByUserId(userId),
      setoranService.getSetoranHistoryByUserId(userId),
      settingsService.getSettingsByUserId(userId),
    ]);

    return {
      user,
      target,
      tabunganData,
      setoranHistory,
      settings,
    };
  },
};
